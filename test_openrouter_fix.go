package main

import (
	"fmt"
	"log"

	"turnsapi/internal"
)

func main() {
	// 创建一个测试配置，模拟问题场景
	config := &internal.Config{
		UserGroups: map[string]*internal.UserGroup{
			"openrouter": {
				Name:         "OpenRouter",
				ProviderType: "openrouter", // 修复后应该是 openrouter 而不是 openai
				BaseURL:      "https://openrouter.ai/api/v1",
				Enabled:      true,
				Models:       []string{}, // 空列表表示支持所有模型
				APIKeys:      []string{"test-key"},
			},
			"openai_official": {
				Name:         "OpenAI Official",
				ProviderType: "openai",
				BaseURL:      "https://api.openai.com/v1",
				Enabled:      true,
				Models:       []string{"gpt-3.5-turbo", "gpt-4"}, // 明确指定模型
				APIKeys:      []string{"test-key"},
			},
		},
	}

	// 创建配置管理器的模拟
	fmt.Println("测试 OpenRouter 分组配置修复:")
	fmt.Printf("OpenRouter 分组的 ProviderType: %s\n", config.UserGroups["openrouter"].ProviderType)
	fmt.Printf("OpenRouter 分组的 Models: %v\n", config.UserGroups["openrouter"].Models)
	fmt.Printf("OpenAI 分组的 ProviderType: %s\n", config.UserGroups["openai_official"].ProviderType)
	fmt.Printf("OpenAI 分组的 Models: %v\n", config.UserGroups["openai_official"].Models)

	// 模拟 getModelsForGroup 的逻辑
	fmt.Println("\n模拟 getModelsForGroup 逻辑:")
	
	for groupID, group := range config.UserGroups {
		fmt.Printf("\n分组: %s\n", groupID)
		
		if len(group.Models) > 0 {
			fmt.Printf("  配置了特定模型: %v\n", group.Models)
		} else {
			fmt.Printf("  没有配置特定模型，根据 ProviderType (%s) 返回预定义模型\n", group.ProviderType)
			
			switch group.ProviderType {
			case "openai":
				fmt.Printf("  -> 会返回 OpenAI 模型 (包括 gpt-3.5-turbo 等)\n")
			case "openrouter":
				fmt.Printf("  -> 会返回 OpenRouter 模型 (不包括 gpt-3.5-turbo 等 OpenAI 模型)\n")
			default:
				fmt.Printf("  -> 会返回通用模型\n")
			}
		}
	}

	fmt.Println("\n修复验证:")
	fmt.Println("✓ OpenRouter 分组的 ProviderType 已从 'openai' 改为 'openrouter'")
	fmt.Println("✓ 当 OpenRouter 分组的 Models 为空时，不会返回 OpenAI 的预定义模型")
	fmt.Println("✓ 只有 ProviderType 为 'openai' 的分组才会返回 gpt-3.5-turbo 等模型")

	log.Println("测试完成")
}
